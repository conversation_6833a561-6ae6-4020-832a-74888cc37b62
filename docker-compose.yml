version: '3.8'

services:
  mcp-server:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: fastmcp-deployment-server
    ports:
      - "8000:8000"
    environment:
      - SERVER_HOST=0.0.0.0
      - SERVER_PORT=8000
      - DEBUG=false
    volumes:
      # 挂载 Docker socket 以便容器内可以使用 Docker
      - /var/run/docker.sock:/var/run/docker.sock
      # 挂载项目目录（可根据需要调整）
      - ./projects:/app/projects:ro
      # 挂载日志目录
      - ./logs:/app/logs
    restart: unless-stopped
    networks:
      - mcp-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # 可选：添加一个 nginx 反向代理
  nginx:
    image: nginx:alpine
    container_name: mcp-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - mcp-server
    restart: unless-stopped
    networks:
      - mcp-network
    profiles:
      - with-nginx

  # 可选：添加 Redis 用于缓存和会话存储
  redis:
    image: redis:7-alpine
    container_name: mcp-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - mcp-network
    profiles:
      - with-redis

networks:
  mcp-network:
    driver: bridge

volumes:
  redis_data:
    driver: local
