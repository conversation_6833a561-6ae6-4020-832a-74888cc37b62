# FastMCP 部署服务器

这是一个基于 FastMCP 的 MCP (Model Context Protocol) 服务器，专门用于通过 docker-compose 进行项目部署。

## 功能特性

- 🚀 使用 FastMCP 构建的高性能 MCP 服务器
- 🐳 支持 docker-compose 项目部署
- 📊 实时部署状态监控
- 📝 详细的部署日志记录
- 🔧 系统信息查询
- 🏥 健康检查支持

## 快速开始

### 1. 克隆项目

```bash
git clone <your-repo-url>
cd mcp_deploy
```

### 2. 使用 docker-compose 启动

```bash
# 启动基础服务
docker-compose up -d

# 或者启动包含 Redis 的完整服务
docker-compose --profile with-redis up -d
```

### 3. 验证服务

```bash
# 检查服务状态
docker-compose ps

# 查看日志
docker-compose logs -f mcp-server

# 健康检查
curl http://localhost:8000/health
```

## 可用工具

### 1. deploy_with_docker_compose
使用 docker-compose 部署项目

**参数:**
- `project_path`: 项目路径
- `compose_file`: docker-compose 文件名 (默认: docker-compose.yml)
- `service_name`: 特定服务名称 (可选)

**示例:**
```python
deploy_with_docker_compose("/path/to/project")
deploy_with_docker_compose("/path/to/project", "docker-compose.prod.yml")
deploy_with_docker_compose("/path/to/project", "docker-compose.yml", "web")
```

### 2. stop_docker_compose
停止 docker-compose 服务

**参数:**
- `project_path`: 项目路径
- `compose_file`: docker-compose 文件名 (默认: docker-compose.yml)
- `service_name`: 特定服务名称 (可选)

### 3. get_deployment_status
获取部署状态

**参数:**
- `deployment_id`: 部署ID (可选，不提供则返回所有部署状态)

### 4. check_docker_compose_status
检查 docker-compose 服务状态

**参数:**
- `project_path`: 项目路径
- `compose_file`: docker-compose 文件名 (默认: docker-compose.yml)

### 5. get_system_info
获取系统信息

## 资源

- `deployment://logs/{deployment_id}`: 获取特定部署的日志
- `deployment://status`: 获取所有部署状态

## 环境变量

| 变量名 | 默认值 | 描述 |
|--------|--------|------|
| SERVER_HOST | 0.0.0.0 | 服务器监听地址 |
| SERVER_PORT | 8000 | 服务器端口 |
| DEBUG | false | 调试模式 |

## 目录结构

```
mcp_deploy/
├── server.py              # 主服务器文件
├── requirements.txt       # Python 依赖
├── Dockerfile            # Docker 镜像构建文件
├── docker-compose.yml    # Docker Compose 配置
├── .dockerignore         # Docker 忽略文件
├── README.md            # 项目文档
└── projects/            # 项目目录 (挂载点)
```

## 开发模式

如果你想在开发模式下运行：

```bash
# 安装依赖
pip install -r requirements.txt

# 设置环境变量
export DEBUG=true

# 运行服务器
python server.py
```

## 使用示例

### 部署一个项目

1. 确保你的项目有 docker-compose.yml 文件
2. 调用部署工具：

```python
# 通过 MCP 客户端调用
result = deploy_with_docker_compose("/path/to/your/project")
print(result)
```

### 监控部署状态

```python
# 获取所有部署状态
status = get_deployment_status()
print(status)

# 获取特定部署状态
status = get_deployment_status("deploy_20241204_143022")
print(status)
```

## 故障排除

### 常见问题

1. **Docker socket 权限问题**
   ```bash
   sudo chmod 666 /var/run/docker.sock
   ```

2. **端口被占用**
   ```bash
   # 修改 docker-compose.yml 中的端口映射
   ports:
     - "8001:8000"  # 使用不同的主机端口
   ```

3. **查看详细日志**
   ```bash
   docker-compose logs -f mcp-server
   ```

## 安全注意事项

- 生产环境中请确保 Docker socket 的安全性
- 考虑使用 Docker-in-Docker 而不是挂载 Docker socket
- 限制容器的网络访问权限
- 定期更新基础镜像和依赖

## 贡献

欢迎提交 Issue 和 Pull Request！

## 许可证

MIT License
