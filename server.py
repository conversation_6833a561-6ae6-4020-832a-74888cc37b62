#!/usr/bin/env python3
"""
FastMCP Server Implementation
A simple MCP server using FastMCP for easy deployment with docker-compose
"""

import os
import json
import asyncio
from typing import Any, Dict, List, Optional
from datetime import datetime

try:
    from mcp.server.fastmcp import FastMCP
    from mcp.types import Resource, Tool, TextContent, ImageContent, EmbeddedResource
except ImportError:
    print("MCP library not found. Installing...")
    import subprocess
    subprocess.check_call(["pip", "install", "mcp"])
    from mcp.server.fastmcp import FastMCP
    from mcp.types import Resource, Tool, TextContent, ImageContent, EmbeddedResource

# 创建 FastMCP 服务器实例
mcp = FastMCP("deployment-server")

# 配置
SERVER_HOST = os.getenv("SERVER_HOST", "0.0.0.0")
SERVER_PORT = int(os.getenv("SERVER_PORT", "8000"))
DEBUG = os.getenv("DEBUG", "false").lower() == "true"

# 存储部署状态的简单内存存储
deployment_status = {}
deployment_logs = {}

@mcp.tool()
def get_system_info() -> str:
    """获取系统信息"""
    import platform
    import psutil
    
    info = {
        "platform": platform.platform(),
        "python_version": platform.python_version(),
        "cpu_count": psutil.cpu_count(),
        "memory_total": f"{psutil.virtual_memory().total / (1024**3):.2f} GB",
        "memory_available": f"{psutil.virtual_memory().available / (1024**3):.2f} GB",
        "timestamp": datetime.now().isoformat()
    }
    
    return json.dumps(info, indent=2)

@mcp.tool()
def deploy_with_docker_compose(
    project_path: str,
    compose_file: str = "docker-compose.yml",
    service_name: Optional[str] = None
) -> str:
    """使用 docker-compose 部署项目"""
    import subprocess
    import os
    
    try:
        # 检查项目路径是否存在
        if not os.path.exists(project_path):
            return f"错误: 项目路径 {project_path} 不存在"
        
        # 检查 docker-compose 文件是否存在
        compose_path = os.path.join(project_path, compose_file)
        if not os.path.exists(compose_path):
            return f"错误: docker-compose 文件 {compose_path} 不存在"
        
        # 构建部署命令
        cmd = ["docker-compose", "-f", compose_path]
        
        if service_name:
            cmd.extend(["up", "-d", service_name])
        else:
            cmd.extend(["up", "-d"])
        
        # 执行部署
        result = subprocess.run(
            cmd,
            cwd=project_path,
            capture_output=True,
            text=True,
            timeout=300  # 5分钟超时
        )
        
        deployment_id = f"deploy_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # 记录部署状态
        deployment_status[deployment_id] = {
            "project_path": project_path,
            "compose_file": compose_file,
            "service_name": service_name,
            "status": "success" if result.returncode == 0 else "failed",
            "timestamp": datetime.now().isoformat(),
            "return_code": result.returncode
        }
        
        # 记录部署日志
        deployment_logs[deployment_id] = {
            "stdout": result.stdout,
            "stderr": result.stderr,
            "command": " ".join(cmd)
        }
        
        if result.returncode == 0:
            return f"部署成功! 部署ID: {deployment_id}\n输出:\n{result.stdout}"
        else:
            return f"部署失败! 部署ID: {deployment_id}\n错误:\n{result.stderr}"
            
    except subprocess.TimeoutExpired:
        return "部署超时 (5分钟)"
    except Exception as e:
        return f"部署过程中发生错误: {str(e)}"

@mcp.tool()
def stop_docker_compose(
    project_path: str,
    compose_file: str = "docker-compose.yml",
    service_name: Optional[str] = None
) -> str:
    """停止 docker-compose 服务"""
    import subprocess
    import os
    
    try:
        compose_path = os.path.join(project_path, compose_file)
        if not os.path.exists(compose_path):
            return f"错误: docker-compose 文件 {compose_path} 不存在"
        
        cmd = ["docker-compose", "-f", compose_path]
        
        if service_name:
            cmd.extend(["stop", service_name])
        else:
            cmd.extend(["down"])
        
        result = subprocess.run(
            cmd,
            cwd=project_path,
            capture_output=True,
            text=True,
            timeout=120
        )
        
        if result.returncode == 0:
            return f"服务停止成功!\n输出:\n{result.stdout}"
        else:
            return f"服务停止失败!\n错误:\n{result.stderr}"
            
    except Exception as e:
        return f"停止服务时发生错误: {str(e)}"

@mcp.tool()
def get_deployment_status(deployment_id: Optional[str] = None) -> str:
    """获取部署状态"""
    if deployment_id:
        if deployment_id in deployment_status:
            status = deployment_status[deployment_id]
            logs = deployment_logs.get(deployment_id, {})
            return json.dumps({
                "deployment_id": deployment_id,
                "status": status,
                "logs": logs
            }, indent=2)
        else:
            return f"未找到部署ID: {deployment_id}"
    else:
        return json.dumps({
            "all_deployments": deployment_status
        }, indent=2)

@mcp.tool()
def check_docker_compose_status(project_path: str, compose_file: str = "docker-compose.yml") -> str:
    """检查 docker-compose 服务状态"""
    import subprocess
    import os
    
    try:
        compose_path = os.path.join(project_path, compose_file)
        if not os.path.exists(compose_path):
            return f"错误: docker-compose 文件 {compose_path} 不存在"
        
        result = subprocess.run(
            ["docker-compose", "-f", compose_path, "ps"],
            cwd=project_path,
            capture_output=True,
            text=True,
            timeout=30
        )
        
        return f"Docker Compose 状态:\n{result.stdout}"
        
    except Exception as e:
        return f"检查状态时发生错误: {str(e)}"

# 资源定义
@mcp.resource("deployment://logs/{deployment_id}")
async def get_deployment_logs(deployment_id: str) -> str:
    """获取特定部署的日志"""
    if deployment_id in deployment_logs:
        logs = deployment_logs[deployment_id]
        return f"部署日志 - {deployment_id}:\n\n命令: {logs.get('command', 'N/A')}\n\n标准输出:\n{logs.get('stdout', 'N/A')}\n\n错误输出:\n{logs.get('stderr', 'N/A')}"
    else:
        return f"未找到部署ID {deployment_id} 的日志"

@mcp.resource("deployment://status")
async def get_all_deployment_status() -> str:
    """获取所有部署状态的资源"""
    return json.dumps(deployment_status, indent=2)

# 提示词定义
@mcp.prompt()
def deployment_help() -> str:
    """部署帮助信息"""
    return """
    # Docker Compose 部署服务器

    这个 MCP 服务器提供了使用 docker-compose 进行项目部署的功能。

    ## 可用工具:
    1. `deploy_with_docker_compose` - 部署项目
    2. `stop_docker_compose` - 停止服务
    3. `get_deployment_status` - 查看部署状态
    4. `check_docker_compose_status` - 检查服务状态
    5. `get_system_info` - 获取系统信息

    ## 使用示例:
    - 部署项目: deploy_with_docker_compose("/path/to/project")
    - 停止服务: stop_docker_compose("/path/to/project")
    - 查看状态: get_deployment_status()
    """

if __name__ == "__main__":
    print(f"启动 FastMCP 服务器在 {SERVER_HOST}:{SERVER_PORT}")
    print(f"调试模式: {DEBUG}")
    
    # 启动服务器
    mcp.run(
        host=SERVER_HOST,
        port=SERVER_PORT,
        debug=DEBUG
    )
