#!/usr/bin/env python3
"""
MCP 客户端示例 - 演示如何与 FastMCP 部署服务器交互
"""

import asyncio
import json
from typing import Any, Dict

try:
    from mcp import ClientSession, StdioServerParameters
    from mcp.client.stdio import stdio_client
except ImportError:
    print("请安装 MCP 客户端库: pip install mcp")
    exit(1)

class MCPDeploymentClient:
    """MCP 部署客户端"""
    
    def __init__(self, server_command: list):
        self.server_command = server_command
        self.session = None
    
    async def connect(self):
        """连接到 MCP 服务器"""
        server_params = StdioServerParameters(
            command=self.server_command[0],
            args=self.server_command[1:] if len(self.server_command) > 1 else []
        )
        
        self.session = await stdio_client(server_params)
        await self.session.initialize()
        print("✅ 已连接到 MCP 服务器")
    
    async def disconnect(self):
        """断开连接"""
        if self.session:
            await self.session.close()
            print("🔌 已断开 MCP 服务器连接")
    
    async def list_tools(self):
        """列出可用工具"""
        if not self.session:
            raise RuntimeError("未连接到服务器")
        
        tools = await self.session.list_tools()
        print("🔧 可用工具:")
        for tool in tools.tools:
            print(f"  - {tool.name}: {tool.description}")
        return tools.tools
    
    async def get_system_info(self):
        """获取系统信息"""
        if not self.session:
            raise RuntimeError("未连接到服务器")
        
        result = await self.session.call_tool("get_system_info", {})
        print("📊 系统信息:")
        print(result.content[0].text)
        return result
    
    async def deploy_project(self, project_path: str, compose_file: str = "docker-compose.yml"):
        """部署项目"""
        if not self.session:
            raise RuntimeError("未连接到服务器")
        
        print(f"🚀 部署项目: {project_path}")
        result = await self.session.call_tool("deploy_with_docker_compose", {
            "project_path": project_path,
            "compose_file": compose_file
        })
        print("部署结果:")
        print(result.content[0].text)
        return result
    
    async def check_deployment_status(self, deployment_id: str = None):
        """检查部署状态"""
        if not self.session:
            raise RuntimeError("未连接到服务器")
        
        args = {}
        if deployment_id:
            args["deployment_id"] = deployment_id
        
        result = await self.session.call_tool("get_deployment_status", args)
        print("📊 部署状态:")
        print(result.content[0].text)
        return result
    
    async def stop_project(self, project_path: str, compose_file: str = "docker-compose.yml"):
        """停止项目"""
        if not self.session:
            raise RuntimeError("未连接到服务器")
        
        print(f"🛑 停止项目: {project_path}")
        result = await self.session.call_tool("stop_docker_compose", {
            "project_path": project_path,
            "compose_file": compose_file
        })
        print("停止结果:")
        print(result.content[0].text)
        return result

async def main():
    """主函数 - 演示客户端使用"""
    # 注意：这里假设服务器通过 stdio 运行
    # 实际使用时可能需要调整为 HTTP 连接
    client = MCPDeploymentClient(["python", "server.py"])
    
    try:
        await client.connect()
        
        # 列出可用工具
        await client.list_tools()
        
        # 获取系统信息
        await client.get_system_info()
        
        # 示例：部署一个项目（请替换为实际路径）
        # await client.deploy_project("/path/to/your/project")
        
        # 检查部署状态
        await client.check_deployment_status()
        
        print("\n🎉 客户端演示完成！")
        
    except Exception as e:
        print(f"❌ 错误: {e}")
    finally:
        await client.disconnect()

if __name__ == "__main__":
    print("🔌 MCP 部署客户端示例")
    print("=" * 50)
    asyncio.run(main())
