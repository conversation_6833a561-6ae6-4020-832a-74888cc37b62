#!/bin/bash

# FastMCP 部署服务器启动脚本

set -e

echo "🚀 启动 FastMCP 部署服务器..."

# 检查 Docker 是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker 未运行，请先启动 Docker"
    exit 1
fi

# 检查 docker-compose 是否可用
if ! command -v docker-compose &> /dev/null; then
    echo "❌ docker-compose 未安装"
    exit 1
fi

# 创建必要的目录
echo "📁 创建必要的目录..."
mkdir -p projects logs

# 构建并启动服务
echo "🔨 构建并启动服务..."
docker-compose up --build -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 检查服务状态
echo "🔍 检查服务状态..."
docker-compose ps

# 健康检查
echo "🏥 执行健康检查..."
if curl -f http://localhost:8000/health > /dev/null 2>&1; then
    echo "✅ 服务启动成功！"
    echo "🌐 服务地址: http://localhost:8000"
    echo "📊 查看日志: docker-compose logs -f mcp-server"
    echo "🛑 停止服务: docker-compose down"
else
    echo "❌ 健康检查失败，查看日志:"
    docker-compose logs mcp-server
    exit 1
fi
